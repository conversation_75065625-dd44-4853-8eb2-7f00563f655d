@echo off
REM Simple launcher for RealtimeSTT Wake Word Auto-Typing

title RealtimeSTT Wake Word Auto-Typing

REM Change to the script directory
cd /d "%~dp0"

echo Starting RealtimeSTT Wake Word Auto-Typing...
echo Say "jarvis" to activate speech-to-text
echo Press Ctrl+C to exit
echo.

REM Run the Python script
python wake_word_auto_typing.py

REM Keep window open if there's an error
if errorlevel 1 (
    echo.
    echo <PERSON>ript exited with an error.
    pause
)
