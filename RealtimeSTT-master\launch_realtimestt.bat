@echo off
REM Portable launcher for RealtimeSTT Wake Word Auto-Typing
REM This batch file can be placed anywhere and will navigate to the RealtimeSTT directory

title RealtimeSTT Wake Word Auto-Typing

REM Navigate directly to the RealtimeSTT-master directory
cd /d "D:\CODEZ\some projects\RealtimeSTT-master"

echo Starting RealtimeSTT Wake Word Auto-Typing...
echo Say "jarvis" to activate speech-to-text
echo Press Ctrl+C to exit
echo.

REM Run the Python script
python wake_word_auto_typing.py

REM Keep window open if there's an error
if errorlevel 1 (
    echo.
    echo Script exited with an error.
    pause
)
