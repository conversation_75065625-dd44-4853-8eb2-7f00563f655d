@echo off
REM Portable launcher for RealtimeSTT Wake Word Auto-Typing
REM This batch file can be placed anywhere and will find the RealtimeSTT-master directory

title RealtimeSTT Wake Word Auto-Typing

echo Locating RealtimeSTT-master directory...

REM Search for RealtimeSTT-master directory starting from common locations
set "SCRIPT_DIR="

REM Check current directory first
if exist "wake_word_auto_typing.py" (
    set "SCRIPT_DIR=%CD%"
    goto :found
)

REM Check if we're already in RealtimeSTT-master
if exist "RealtimeSTT-master\wake_word_auto_typing.py" (
    set "SCRIPT_DIR=%CD%\RealtimeSTT-master"
    goto :found
)

REM Search common locations
for %%d in (
    "%USERPROFILE%\Desktop"
    "%USERPROFILE%\Documents"
    "%USERPROFILE%\Downloads"
    "C:\"
    "D:\"
) do (
    if exist "%%d\RealtimeSTT-master\wake_word_auto_typing.py" (
        set "SCRIPT_DIR=%%d\RealtimeSTT-master"
        goto :found
    )
)

REM Search all drives for RealtimeSTT-master
echo Searching all drives for RealtimeSTT-master directory...
for %%i in (C D E F G H I J K L M N O P Q R S T U V W X Y Z) do (
    if exist "%%i:\" (
        for /f "delims=" %%j in ('dir "%%i:\RealtimeSTT-master" /ad /b /s 2^>nul') do (
            if exist "%%j\wake_word_auto_typing.py" (
                set "SCRIPT_DIR=%%j"
                goto :found
            )
        )
    )
)

REM If not found, show error
echo ERROR: Could not locate RealtimeSTT-master directory with wake_word_auto_typing.py
echo Please ensure the RealtimeSTT-master folder exists on your system.
pause
exit /b 1

:found
echo Found RealtimeSTT at: %SCRIPT_DIR%
echo.

REM Change to the script directory
cd /d "%SCRIPT_DIR%"

echo Starting RealtimeSTT Wake Word Auto-Typing...
echo Say "jarvis" to activate speech-to-text
echo Press Ctrl+C to exit
echo.

REM Run the Python script
python wake_word_auto_typing.py

REM Keep window open if there's an error
if errorlevel 1 (
    echo.
    echo Script exited with an error.
    pause
)
