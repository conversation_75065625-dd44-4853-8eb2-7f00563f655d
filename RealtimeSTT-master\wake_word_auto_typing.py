"""
Wake Word + Auto-typing example for RealtimeSTT

This example demonstrates wake word activation combined with auto-typing functionality.
The application will:
1. Listen continuously for a wake word (like "jarvis")
2. When the wake word is detected, start recording your speech
3. Transcribe the speech to text using the local Whisper model
4. Automatically type the transcribed text into whatever text field is currently active/focused

Features:
- Wake word activation (no constant recording)
- Local processing only (no API tokens consumed)
- Auto-typing into any active text input field
- Visual feedback and status updates

Usage:
1. Run this script
2. Open any text editor, word processor, or text input field
3. Click in the text field to give it focus
4. Say the wake word "jarvis" (or your chosen wake word)
5. Speak your message
6. Watch as your speech is automatically typed into the active text field

Press Ctrl+C to exit.
"""

import os
import sys
import time
import threading

# Handle Windows-specific torch audio initialization
if os.name == "nt" and (3, 8) <= sys.version_info < (3, 99):
    try:
        from torchaudio._extension.utils import _init_dll_path
        _init_dll_path()
    except ImportError:
        pass

from RealtimeSTT import AudioToTextRecorder

# Audio feedback functionality
try:
    import pygame
    PYGAME_AVAILABLE = True
except ImportError:
    PYGAME_AVAILABLE = False
    pygame = None


class AudioFeedback:
    """Handles audio feedback for wake word detection and listening states"""

    def __init__(self, enable_audio=True, sounds_dir="sounds"):
        self.enable_audio = enable_audio and PYGAME_AVAILABLE
        self.sounds_dir = sounds_dir
        self.sounds = {}

        if self.enable_audio:
            try:
                # Initialize pygame mixer for audio playback
                pygame.mixer.pre_init(frequency=22050, size=-16, channels=2, buffer=512)
                pygame.mixer.init()
                self._load_sounds()
            except Exception as e:
                print(f"Warning: Could not initialize audio feedback: {e}")
                self.enable_audio = False

    def _load_sounds(self):
        """Load audio files for different states"""
        sound_files = {
            'wake_detected': 'wake_detected.wav',  # Sound when "jarvis" is detected
            'listening_start': 'listening_start.wav',  # Sound when listening starts
            'listening_stop': 'listening_stop.wav',   # Sound when listening stops
        }

        for sound_name, filename in sound_files.items():
            filepath = os.path.join(self.sounds_dir, filename)
            if os.path.exists(filepath):
                try:
                    self.sounds[sound_name] = pygame.mixer.Sound(filepath)
                    # Set volume to a reasonable level (0.0 to 1.0)
                    self.sounds[sound_name].set_volume(0.7)
                except Exception as e:
                    print(f"Warning: Could not load sound {filename}: {e}")
            else:
                print(f"Warning: Sound file not found: {filepath}")

    def play_sound(self, sound_name):
        """Play a specific sound in a separate thread to avoid blocking"""
        if self.enable_audio and sound_name in self.sounds:
            def play_async():
                try:
                    self.sounds[sound_name].play()
                except Exception as e:
                    print(f"Warning: Could not play sound {sound_name}: {e}")

            # Play sound in a separate thread to avoid blocking the main process
            threading.Thread(target=play_async, daemon=True).start()


class WakeWordAutoTyper:
    def __init__(self, enable_audio_feedback=True, sounds_dir="sounds"):
        self.wake_word_detected = False
        self.typing_active = False
        self.last_realtime_text = ""
        self.current_typed_length = 0

        # Initialize audio feedback system
        self.audio_feedback = AudioFeedback(enable_audio_feedback, sounds_dir)
    
    def on_wakeword_detected(self):
        """Callback when wake word is detected"""
        self.wake_word_detected = True
        # Play wake word detection sound
        self.audio_feedback.play_sound('wake_detected')

    def on_wakeword_timeout(self):
        """Callback when wake word times out"""
        # Play listening stop sound when wake word times out
        self.audio_feedback.play_sound('listening_stop')

    def on_wakeword_detection_start(self):
        """Callback when wake word detection starts"""
        pass

    def on_wakeword_detection_end(self):
        """Callback when wake word detection ends"""
        pass

    def on_recording_start(self):
        """Callback when recording starts"""
        # Play listening start sound when recording begins
        self.audio_feedback.play_sound('listening_start')

    def on_recording_stop(self):
        """Callback when recording stops"""
        # Play listening stop sound when recording ends
        self.audio_feedback.play_sound('listening_stop')

    def on_typing_start(self):
        """Callback when auto-typing starts"""
        self.typing_active = True

    def on_typing_complete(self):
        """Callback when auto-typing completes (not used since we disabled built-in auto-typing)"""
        self.typing_active = False

    def on_typing_error(self, error):
        """Callback when auto-typing encounters an error (not used since we disabled built-in auto-typing)"""
        self.typing_active = False

    def on_realtime_transcription_update(self, text):
        """Callback for real-time transcription updates - display only, no typing"""
        if not text or not text.strip():
            return

        # Store text but don't display real-time updates for minimal output
        if text != self.last_realtime_text:
            self.last_realtime_text = text

    def on_realtime_transcription_stabilized(self, text):
        """Callback for stabilized real-time transcription"""
        # No output for stabilized text to keep minimal interface
        pass

    def process_text(self, text):
        """Process final transcribed text and type it into the active text field"""
        if text.strip():
            # Type the final, most accurate transcription into the text field
            try:
                import pyautogui
                pyautogui.typewrite(text, interval=0.01)  # Type final text
                pyautogui.typewrite(" ", interval=0.01)   # Add space after transcription
            except Exception as e:
                print(f"Error: {e}")

            # Reset counters for next session
            self.last_realtime_text = ""
            self.current_typed_length = 0
    
    def run(self):
        print("Ready - Say 'jarvis' then speak your message")
        print("Press Ctrl+C to exit")
        print()
        
        try:
            # Initialize the recorder with wake word + real-time auto-typing
            recorder = AudioToTextRecorder(
                # Model configuration
                model="small",  # Upgraded model for better transcription accuracy
                language="en",  # Set to your preferred language

                # Real-time transcription settings - DISABLED to prevent duplicate typing
                enable_realtime_transcription=False,  # Disable to prevent duplicate text output
                realtime_model_type="tiny",  # Fast model for real-time processing
                realtime_processing_pause=0.05,  # Process every 50ms for responsiveness
                on_realtime_transcription_update=self.on_realtime_transcription_update,
                on_realtime_transcription_stabilized=self.on_realtime_transcription_stabilized,

                # Wake word configuration
                wakeword_backend="pvporcupine",  # Use Porcupine backend
                wake_words="jarvis",  # You can change this to other supported wake words
                wake_words_sensitivity=0.6,  # Adjust sensitivity (0.0-1.0)
                wake_word_timeout=5,  # Timeout after wake word detection
                wake_word_activation_delay=0,  # Immediate activation

                # Wake word callbacks
                on_wakeword_detected=self.on_wakeword_detected,
                on_wakeword_timeout=self.on_wakeword_timeout,
                on_wakeword_detection_start=self.on_wakeword_detection_start,
                on_wakeword_detection_end=self.on_wakeword_detection_end,

                # Auto-typing configuration - DISABLED (we handle it manually in process_text)
                enable_auto_typing=False,  # Disable built-in auto-typing completely
                auto_typing_delay=0.01,
                auto_typing_fail_safe=True,
                auto_typing_add_space=False,  # We handle spacing manually

                # Auto-typing callbacks
                on_auto_typing_start=self.on_typing_start,
                on_auto_typing_complete=self.on_typing_complete,
                on_auto_typing_error=self.on_typing_error,

                # Recording callbacks
                on_recording_start=self.on_recording_start,
                on_recording_stop=self.on_recording_stop,

                # Voice activity detection settings
                silero_sensitivity=0.4,  # Default value for better voice detection
                webrtc_sensitivity=3,
                post_speech_silence_duration=1.5,  # Reduced for faster response
                min_length_of_recording=0.5,  # Default minimum for complete words

                # Audio quality settings
                beam_size=5,  # Default beam size for better accuracy
                beam_size_realtime=3,  # Beam size for real-time processing
                faster_whisper_vad_filter=True,  # Enable VAD filtering for cleaner audio
                normalize_audio=True,  # Normalize audio levels for consistency

                # UI settings
                spinner=False,  # Disable spinner for clean output

                # Logging settings
                no_log_file=True,  # Disable log file creation to eliminate unnecessary file I/O
            )
            
            # Main loop - continuously listen for wake word and process speech
            while True:
                try:
                    # This will wait for wake word, then record and get transcription
                    text = recorder.text()  # Get text without callback to prevent duplicate processing

                    # Process the text manually to ensure single execution
                    if text and text.strip():
                        self.process_text(text)

                except KeyboardInterrupt:
                    print("\nStopping...")
                    break
                except Exception as e:
                    print(f"Error: {e}")
                    time.sleep(1)
                    
        except ImportError as e:
            if "pyautogui" in str(e):
                print("Error: pyautogui is required for auto-typing functionality.")
                print("Install it with: pip install pyautogui")
            elif "pvporcupine" in str(e):
                print("Error: pvporcupine is required for wake word detection.")
                print("Install it with: pip install pvporcupine")
            else:
                print(f"Import error: {e}")
            sys.exit(1)

        except Exception as e:
            print(f"Failed to initialize recorder: {e}")
            sys.exit(1)

        finally:
            try:
                recorder.shutdown()
            except:
                pass


def main():
    typer = WakeWordAutoTyper()
    typer.run()


if __name__ == "__main__":
    main()
